name: Lint

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22.14.0"

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm --filter '*' install --frozen-lockfile

      - name: Run ESLint
        run: pnpm -r exec eslint

      - name: Run TypeScript check
        run: pnpm -r exec tsc --noEmit

      - name: Run Prettier check
        run: pnpm exec prettier --check .
