import { ThreadData } from "@/web/contexts/threadContext";

export class ThreadStorage {
  private static readonly STORAGE_KEY = "pearl-threads";
  private static readonly CURRENT_THREAD_KEY = "pearl-current-thread";

  static saveThreads(threads: ThreadData[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(threads));
    } catch (error) {
      console.error("Failed to save threads to localStorage:", error);
    }
  }

  static loadThreads(): ThreadData[] {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error("Failed to load threads from localStorage:", error);
      return [];
    }
  }

  static saveCurrentThreadId(threadId: string | null): void {
    try {
      if (threadId) {
        localStorage.setItem(this.CURRENT_THREAD_KEY, threadId);
      } else {
        localStorage.removeItem(this.CURRENT_THREAD_KEY);
      }
    } catch (error) {
      console.error("Failed to save current thread ID:", error);
    }
  }

  static loadCurrentThreadId(): string | null {
    try {
      return localStorage.getItem(this.CURRENT_THREAD_KEY);
    } catch (error) {
      console.error("Failed to load current thread ID:", error);
      return null;
    }
  }

  static clearAll(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.CURRENT_THREAD_KEY);
    } catch (error) {
      console.error("Failed to clear thread storage:", error);
    }
  }

  static getThreadsForAccount(accountId: string): ThreadData[] {
    const allThreads = this.loadThreads();
    return allThreads.filter(thread => thread.accountId === accountId);
  }

  static saveThread(thread: ThreadData): void {
    const threads = this.loadThreads();
    const existingIndex = threads.findIndex(t => t.id === thread.id);
    
    if (existingIndex >= 0) {
      threads[existingIndex] = thread;
    } else {
      threads.push(thread);
    }
    
    this.saveThreads(threads);
  }

  static deleteThread(threadId: string): void {
    const threads = this.loadThreads();
    const filteredThreads = threads.filter(t => t.id !== threadId);
    this.saveThreads(filteredThreads);
    
    // Clear current thread if it was deleted
    const currentThreadId = this.loadCurrentThreadId();
    if (currentThreadId === threadId) {
      this.saveCurrentThreadId(null);
    }
  }
}
