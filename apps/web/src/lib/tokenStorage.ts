import { TokenStorageAdapter } from "@/api-client";

class TokenStorage implements TokenStorageAdapter {
  private accessTokenKey: string = "accessToken";
  private refreshTokenKey: string = "refreshToken";
  getAccessToken(): string | null {
    return localStorage.getItem(this.accessTokenKey);
  }
  getRefreshToken(): string | null {
    return localStorage.getItem(this.refreshTokenKey);
  }
  saveTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.accessTokenKey, accessToken);
    localStorage.setItem(this.refreshTokenKey, refreshToken);
  }
  clearTokens(): void {
    localStorage.removeItem(this.accessTokenKey);
    localStorage.removeItem(this.refreshTokenKey);
  }
}

export const tokenStorage = new TokenStorage();
