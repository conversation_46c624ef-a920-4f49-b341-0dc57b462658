import { Account } from "@/api-hooks";
import {
  ReactNode,
  createContext,
  useContext,
  useState,
  useEffect,
} from "react";
import { ThreadStorage } from "@/web/lib/threadStorage";

export interface ThreadData {
  id: string;
  accountId: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages: readonly any[]; // Will store the thread messages
}

type ThreadContextType = {
  threads: ThreadData[];
  currentThreadId: string | null;
  createThread: (account: Account) => string;
  switchThread: (threadId: string) => void;
  deleteThread: (threadId: string) => void;
  getThreadsForAccount: (accountId: string) => ThreadData[];
  getCurrentThread: () => ThreadData | null;
  updateThreadTitle: (threadId: string, title: string) => void;
  saveThreadMessages: (threadId: string, messages: readonly any[]) => void;
};

const ThreadContext = createContext<ThreadContextType | undefined>(undefined);

export function ThreadProvider({ children }: { children: ReactNode }) {
  const [threads, setThreads] = useState<ThreadData[]>([]);
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);

  // Load threads from storage on mount
  useEffect(() => {
    const savedThreads = ThreadStorage.loadThreads();
    const savedCurrentThreadId = ThreadStorage.loadCurrentThreadId();

    setThreads(savedThreads);
    setCurrentThreadId(savedCurrentThreadId);
  }, []);

  // Save threads to storage whenever threads change
  useEffect(() => {
    ThreadStorage.saveThreads(threads);
  }, [threads]);

  // Save current thread ID whenever it changes
  useEffect(() => {
    ThreadStorage.saveCurrentThreadId(currentThreadId);
  }, [currentThreadId]);

  const createThread = (account: Account): string => {
    const newThread: ThreadData = {
      id: `thread-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      accountId: account.crmId,
      title: "New Conversation",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messages: [],
    };

    setThreads((prev) => [...prev, newThread]);
    setCurrentThreadId(newThread.id);
    return newThread.id;
  };

  const switchThread = (threadId: string) => {
    const thread = threads.find((t) => t.id === threadId);
    if (thread) {
      setCurrentThreadId(threadId);
    }
  };

  const deleteThread = (threadId: string) => {
    setThreads((prev) => prev.filter((t) => t.id !== threadId));
    if (currentThreadId === threadId) {
      setCurrentThreadId(null);
    }
  };

  const getThreadsForAccount = (accountId: string): ThreadData[] => {
    return threads.filter((thread) => thread.accountId === accountId);
  };

  const getCurrentThread = (): ThreadData | null => {
    if (!currentThreadId) return null;
    return threads.find((t) => t.id === currentThreadId) || null;
  };

  const updateThreadTitle = (threadId: string, title: string) => {
    setThreads((prev) =>
      prev.map((thread) =>
        thread.id === threadId
          ? { ...thread, title, updatedAt: new Date().toISOString() }
          : thread,
      ),
    );
  };

  const saveThreadMessages = (threadId: string, messages: readonly any[]) => {
    setThreads((prev) =>
      prev.map((thread) =>
        thread.id === threadId
          ? { ...thread, messages, updatedAt: new Date().toISOString() }
          : thread,
      ),
    );
  };

  return (
    <ThreadContext.Provider
      value={{
        threads,
        currentThreadId,
        createThread,
        switchThread,
        deleteThread,
        getThreadsForAccount,
        getCurrentThread,
        updateThreadTitle,
        saveThreadMessages,
      }}
    >
      {children}
    </ThreadContext.Provider>
  );
}

export function useThreadManager() {
  const context = useContext(ThreadContext);
  if (context === undefined) {
    throw new Error("useThreadManager must be used within a ThreadProvider");
  }
  return context;
}
