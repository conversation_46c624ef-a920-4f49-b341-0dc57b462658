import { Account } from "@/api-hooks";
import { ReactNode, createContext, useContext, useState } from "react";

type AccountContextType = {
  selectedAccount: Account | null;
  setSelectedAccount: (account: Account) => void;
  allAccounts: Account[];
  setAllAccounts: (accounts: Account[]) => void;
};

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export function AccountProvider({ children }: { children: ReactNode }) {
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [allAccounts, setAllAccounts] = useState<Account[]>([]);

  return (
    <AccountContext.Provider
      value={{
        selectedAccount,
        setSelectedAccount,
        allAccounts,
        setAllAccounts,
      }}
    >
      {children}
    </AccountContext.Provider>
  );
}

export function useAccount() {
  const context = useContext(AccountContext);
  if (context === undefined) {
    throw new Error("useAccount must be used within an AccountProvider");
  }
  return context;
}
