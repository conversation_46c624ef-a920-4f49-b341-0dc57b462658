"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";

import { Thread } from "@/web/components/chat/thread";
import { SidebarTrigger } from "@/web/components/ui/sidebar";
import { useAccount } from "@/web/contexts/accountContext";

export default function Account() {
  const { accountId: accountIdFromParams } = useParams();

  const { selectedAccount, setSelectedAccount, allAccounts } = useAccount();

  // Check/set selected account based on URL params
  useEffect(() => {
    const accountFromParams = allAccounts.find(
      (account) => account.crmId === accountIdFromParams,
    );
    if (
      accountFromParams &&
      (!selectedAccount || selectedAccount.crmId !== accountIdFromParams)
    ) {
      setSelectedAccount(accountFromParams);
    }
  }, [accountIdFromParams, allAccounts, selectedAccount, setSelectedAccount]);

  return (
    <>
      <SidebarTrigger className="md:hidden" />
      <Thread />
    </>
  );
}
