"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";

import { Thread } from "@/web/components/chat/thread";
import { SidebarTrigger } from "@/web/components/ui/sidebar";
import { useAccount } from "@/web/contexts/accountContext";
import { useThreadManager } from "@/web/contexts/threadContext";

export default function Account() {
  const { accountId: accountIdFromParams } = useParams();

  const { selectedAccount, setSelectedAccount, allAccounts } = useAccount();
  const { getThreadsForAccount, switchThread, createThread, currentThreadId } =
    useThreadManager();

  // Check/set selected account based on URL params and handle thread switching
  useEffect(() => {
    const accountFromParams = allAccounts.find(
      (account) => account.crmId === accountIdFromParams,
    );
    if (
      accountFromParams &&
      (!selectedAccount || selectedAccount.crmId !== accountIdFromParams)
    ) {
      setSelectedAccount(accountFromParams);

      // Handle thread switching for the new account
      const accountThreads = getThreadsForAccount(accountFromParams.crmId);
      if (accountThreads.length > 0) {
        // If there's no current thread or current thread doesn't belong to this account
        const currentThread = accountThreads.find(
          (t) => t.id === currentThreadId,
        );
        if (!currentThread) {
          // Switch to the most recently updated thread for this account
          const mostRecentThread = accountThreads.sort(
            (a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
          )[0];
          switchThread(mostRecentThread.id);
        }
      } else {
        // Create a new thread for this account if none exist
        createThread(accountFromParams);
      }
    }
  }, [
    accountIdFromParams,
    allAccounts,
    selectedAccount,
    setSelectedAccount,
    getThreadsForAccount,
    switchThread,
    createThread,
    currentThreadId,
  ]);

  return (
    <>
      <SidebarTrigger className="md:hidden" />
      <Thread />
    </>
  );
}
