"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { useAccount } from "@/web/contexts/accountContext";

export default function Account() {
  const router = useRouter();
  const { allAccounts, selectedAccount, setSelectedAccount } = useAccount();
  useEffect(() => {
    if (allAccounts.length > 0) {
      const firstAccount = allAccounts[0];
      if (!selectedAccount) {
        // If no account is selected, set the first account as the selected one
        setSelectedAccount(firstAccount);
      }
      router.push(`/account/${(selectedAccount || firstAccount).crmId}`);
    }
  }, [allAccounts, selectedAccount, setSelectedAccount, router]);
  return null;
}
