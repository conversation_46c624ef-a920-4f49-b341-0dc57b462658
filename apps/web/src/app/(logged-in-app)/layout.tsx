import AuthenticatedRoute from "@/web/components/AuthenticatedRoute";
import MainContentWrapper from "@/web/components/MainContentWrapper";
import Sidebar from "@/web/components/sidebar/Sidebar";
import { SidebarProvider } from "@/web/components/ui/sidebar";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <AuthenticatedRoute>
      <SidebarProvider>
        <Sidebar />
        <MainContentWrapper>{children}</MainContentWrapper>
      </SidebarProvider>
    </AuthenticatedRoute>
  );
}
