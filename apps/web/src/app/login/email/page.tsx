"use client";

import { useAuth } from "@/api-client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Suspense } from "react";

import CodeAuthForm from "@/web/components/CodeAuthForm";
import EmailAuthForm from "@/web/components/EmailAuthForm";
import Loader from "@/web/components/Loader";
import RedirectIfAuthenticatedRoute from "@/web/components/RedirectIfAuthenticatedRoute";

function EmailLogin() {
  const { requestOtc, verifyOtcToken, verifyOtcCode } = useAuth();
  const searchParams = useSearchParams();
  const queryToken = searchParams.get("token");
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const validateQueryToken = async () => {
      if (!queryToken) return;
      setLoading(true);
      try {
        await verifyOtcToken(queryToken);
        setToken(queryToken);
      } catch (error) {
        console.error("Error verifying OTC token:", error);
        // Clear the token from the URL query parameters
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("token");
        router.replace(
          `${window.location.pathname}${newSearchParams.toString() ? `?${newSearchParams.toString()}` : ""}`,
        );
      } finally {
        setLoading(false);
      }
    };
    validateQueryToken();
  }, [queryToken, verifyOtcToken, searchParams, router]);

  const onSubmitEmail = async (email: string) => {
    const response = await requestOtc(email);
    setToken(response.token);
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : token ? (
        <CodeAuthForm token={token} onSubmit={verifyOtcCode} />
      ) : (
        <EmailAuthForm onSubmit={onSubmitEmail} />
      )}
    </>
  );
}

export default function EmailLoginPage() {
  return (
    <RedirectIfAuthenticatedRoute>
      <Suspense fallback={<Loader />}>
        <EmailLogin />
      </Suspense>
    </RedirectIfAuthenticatedRoute>
  );
}
