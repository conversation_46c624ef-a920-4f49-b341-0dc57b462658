"use client";

import { FormEvent, useState } from "react";

import { Button } from "@/web/components/ui/button";

export default function EmailAuthForm({
  onSubmit,
}: {
  onSubmit: (email: string) => void;
}) {
  const [email, setEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  const handleChange = (e: FormEvent) => {
    const { value } = e.target as HTMLInputElement;
    setEmail(value);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage("");
    setError("");
    try {
      onSubmit(email);
    } catch (err) {
      console.error("Error during email submission:", err);
      setError("An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h1>Sign in to your account</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            value={email}
            onChange={handleChange}
            required
          />
        </div>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Loading..." : "Send me a magic link"}
        </Button>
        {/* Contact us is a <NAME_EMAIL>*/}
        <p>
          Don't have an account?{" "}
          <a href="mailto:<EMAIL>">Contact us</a>
        </p>
      </form>

      {message && <p>{message}</p>}
      {error && <p>{error}</p>}
    </div>
  );
}
