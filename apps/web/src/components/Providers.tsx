"use client";

import { ApiClientProvider } from "@/api-client";
import { QueryClientProvider } from "@/api-hooks";

import ThreadAwareRuntimeProvider from "@/web/components/chat/ThreadAwareRuntimeProvider";
import { AccountProvider } from "@/web/contexts/accountContext";
import { ThreadProvider } from "@/web/contexts/threadContext";
import { AuthProvider } from "@/web/contexts/authContext";
import { apiClient } from "@/web/lib/apiClient";
import { tokenStorage } from "@/web/lib/tokenStorage";

export default function Providers({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ApiClientProvider apiClient={apiClient} tokenStorage={tokenStorage}>
      <AuthProvider>
        <QueryClientProvider>
          <AccountProvider>
            <ThreadProvider>
              <ThreadAwareRuntimeProvider>
                {children}
              </ThreadAwareRuntimeProvider>
            </ThreadProvider>
          </AccountProvider>
        </QueryClientProvider>
      </AuthProvider>
    </ApiClientProvider>
  );
}
