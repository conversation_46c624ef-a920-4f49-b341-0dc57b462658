"use client";

import { ApiClientProvider } from "@/api-client";
import { QueryClientProvider } from "@/api-hooks";

import ChatRuntimeProvider from "@/web/components/chat/ChatRuntimeProvider";
import { AccountProvider } from "@/web/contexts/accountContext";
import { AuthProvider } from "@/web/contexts/authContext";
import { apiClient } from "@/web/lib/apiClient";
import { tokenStorage } from "@/web/lib/tokenStorage";

export default function Providers({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ApiClientProvider apiClient={apiClient} tokenStorage={tokenStorage}>
      <AuthProvider>
        <QueryClientProvider>
          <AccountProvider>
            <ChatRuntimeProvider>{children}</ChatRuntimeProvider>
          </AccountProvider>
        </QueryClientProvider>
      </AuthProvider>
    </ApiClientProvider>
  );
}
