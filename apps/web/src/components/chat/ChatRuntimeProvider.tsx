import { Account } from "@/api-hooks";
import {
  AssistantRuntimeProvider,
  type ChatModelAdapter,
  type ThreadMessage,
  useLocalRuntime,
  useThreadList,
} from "@assistant-ui/react";

import { useAccount } from "@/web/contexts/accountContext";

import { apiClient } from "@/web/lib/apiClient";

interface MessageContentPart {
  type: "text";
  text: string;
}

interface AssistantMessageChunk {
  content?: MessageContentPart[];
  metadata?: {
    custom?: Record<string, unknown>;
  };
}

interface StreamProcessingResult {
  accumulatedText: string;
  threadId: string | null;
}

function createModelAdapter(account: Account | null): ChatModelAdapter {
  return {
    async *run({ messages, abortSignal }) {
      const threadId = getThreadIdToSend(messages);
      const userMessageText = getLatestUserMessageText(messages);

      if (!userMessageText) {
        return;
      }

      let response: Response;
      try {
        response = await apiClient.post(
          "/agent/chat_stream",
          {
            message: userMessageText,
            thread_id: threadId,
            crm_account_id: account?.crmId,
          },
          abortSignal,
        );
      } catch (error) {
        yield formatErrorMessage(
          `Error calling API: ${(error as Error).message}`,
        );
        return;
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Failed to get SSE response reader");
      }

      let streamResult: StreamProcessingResult = {
        accumulatedText: "",
        threadId: null,
      };

      try {
        const eventStreamProcessor = processStreamedEvents(reader);
        let currentEvent = await eventStreamProcessor.next();
        while (!currentEvent.done) {
          yield currentEvent.value;
          currentEvent = await eventStreamProcessor.next();
        }
        streamResult = currentEvent.value;
      } catch (error) {
        yield formatErrorMessage(
          `Client-side stream processing error: ${(error as Error).message}`,
        );
      } finally {
        reader.releaseLock();
        if (streamResult.threadId && streamResult.accumulatedText === "") {
          yield {
            metadata: {
              custom: { threadId: streamResult.threadId },
            },
          };
        }
      }
    },
  };
}

function ThreadWatcher() {
  const threads = useThreadList((m) => m.threads);
  console.log(
    "------------------------------------------------------------------",
  );
  console.log("threads", threads);
  console.log(
    "------------------------------------------------------------------",
  );
  return null;
}

export default function ChatRuntimeProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { selectedAccount } = useAccount();
  const runtime = useLocalRuntime(createModelAdapter(selectedAccount));

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <ThreadWatcher />
      {children}
    </AssistantRuntimeProvider>
  );
}

async function* processStreamedEvents(
  reader: ReadableStreamDefaultReader<Uint8Array>,
): AsyncGenerator<AssistantMessageChunk, StreamProcessingResult, undefined> {
  const decoder = new TextDecoder();

  let accumulatedText = "";
  let currentEventName: string | null = null;
  let threadId: string | null = null;

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const eventLines = chunk.split("\n").filter((line) => line.trim() !== "");

      for (const line of eventLines) {
        if (line.startsWith("event:")) {
          currentEventName = line.substring(6).trim();
          continue;
        }

        if (line.startsWith("data:")) {
          const dataContent = line.substring(5).trim();
          const parsedData = JSON.parse(dataContent);

          if (currentEventName === "metadata") {
            if (parsedData.thread_id) {
              threadId = parsedData.thread_id;
            }
            currentEventName = null;
          } else if (currentEventName === "message") {
            const contentDelta = parsedData.content || "";
            if (contentDelta) {
              accumulatedText += contentDelta;
              yield {
                content: [{ type: "text", text: accumulatedText }],
                metadata: { custom: { threadId } },
              };
            }
          }
        }
      }
    }
  } catch (e) {
    yield formatErrorMessage(
      `Stream Events processing error: ${(e as Error).message}`,
    );
  }
  return { accumulatedText, threadId };
}

function getThreadIdToSend(messages: readonly ThreadMessage[]): string | null {
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i];
    if (message.role === "assistant" && message.metadata?.custom?.threadId) {
      return message.metadata.custom.threadId as string;
    }
  }
  return null;
}

function getLatestUserMessageText(
  messages: readonly ThreadMessage[],
): string | null {
  if (messages.length === 0) {
    return null;
  }
  const lastMessage = messages[messages.length - 1];
  if (lastMessage.role !== "user") {
    return null;
  }

  if (lastMessage.content) {
    for (const part of lastMessage.content) {
      if (part.type === "text") {
        return part.text;
      }
    }
  }
  return null;
}

function formatErrorMessage(message: string): AssistantMessageChunk {
  return { content: [{ type: "text", text: message }] };
}
