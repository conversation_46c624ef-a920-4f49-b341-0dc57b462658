import { Account } from "@/api-hooks";
import {
  AssistantRuntimeProvider,
  type ChatModelAdapter,
  type ThreadMessage,
  useLocalRuntime,
  useExternalStoreRuntime,
} from "@assistant-ui/react";
import { useEffect, useMemo, useRef } from "react";

import { useAccount } from "@/web/contexts/accountContext";
import { useThreadManager } from "@/web/contexts/threadContext";
import { apiClient } from "@/web/lib/apiClient";

interface MessageContentPart {
  type: "text";
  text: string;
}

interface AssistantMessageChunk {
  content?: MessageContentPart[];
  metadata?: {
    custom?: Record<string, unknown>;
  };
}

interface StreamProcessingResult {
  accumulatedText: string;
  threadId: string | null;
}

function createModelAdapter(
  account: Account | null,
  backendThreadId: string | null,
): ChatModelAdapter {
  return {
    async *run({ messages, abortSignal }) {
      const userMessageText = getLatestUserMessageText(messages);

      if (!userMessageText) {
        return;
      }

      let response: Response;
      try {
        response = await apiClient.post(
          "/agent/chat_stream",
          {
            message: userMessageText,
            thread_id: backendThreadId,
            crm_account_id: account?.crmId,
          },
          abortSignal,
        );
      } catch (error) {
        yield formatErrorMessage(
          `Error calling API: ${(error as Error).message}`,
        );
        return;
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Failed to get SSE response reader");
      }

      let streamResult: StreamProcessingResult = {
        accumulatedText: "",
        threadId: null,
      };

      try {
        const eventStreamProcessor = processStreamedEvents(reader);
        let currentEvent = await eventStreamProcessor.next();
        while (!currentEvent.done) {
          yield currentEvent.value;
          currentEvent = await eventStreamProcessor.next();
        }
        streamResult = currentEvent.value;
      } catch (error) {
        yield formatErrorMessage(
          `Client-side stream processing error: ${(error as Error).message}`,
        );
      } finally {
        reader.releaseLock();
        if (streamResult.threadId && streamResult.accumulatedText === "") {
          yield {
            metadata: {
              custom: { threadId: streamResult.threadId },
            },
          };
        }
      }
    },
  };
}

export default function ThreadAwareRuntimeProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { selectedAccount } = useAccount();
  const {
    getCurrentThread,
    saveThreadMessages,
    currentThreadId,
    updateThreadTitle,
  } = useThreadManager();

  const currentThread = getCurrentThread();
  const backendThreadId = useRef<string | null>(null);

  // Extract backend thread ID from the current thread's messages
  useEffect(() => {
    if (currentThread?.messages) {
      const lastAssistantMessage = [...currentThread.messages]
        .reverse()
        .find(
          (msg: any) =>
            msg.role === "assistant" && msg.metadata?.custom?.threadId,
        );

      if (lastAssistantMessage) {
        backendThreadId.current = lastAssistantMessage.metadata.custom.threadId;
      } else {
        backendThreadId.current = null;
      }
    } else {
      backendThreadId.current = null;
    }
  }, [currentThread]);

  const runtime = useLocalRuntime(
    createModelAdapter(selectedAccount, backendThreadId.current),
    {
      initialMessages: currentThread?.messages || [],
    },
  );

  // Save messages whenever they change and update thread title if needed
  useEffect(() => {
    if (currentThreadId && runtime) {
      const unsubscribe = runtime.subscribe(() => {
        const messages = runtime.getState().messages;
        saveThreadMessages(currentThreadId, messages);

        // Update thread title based on first user message if it's still "New Conversation"
        const currentThread = getCurrentThread();
        if (
          currentThread &&
          currentThread.title === "New Conversation" &&
          messages.length > 0
        ) {
          const firstUserMessage = messages.find((msg) => msg.role === "user");
          if (firstUserMessage && firstUserMessage.content) {
            const textContent = firstUserMessage.content.find(
              (part) => part.type === "text",
            );
            if (textContent) {
              const title =
                textContent.text.length > 50
                  ? `${textContent.text.substring(0, 50)}...`
                  : textContent.text;
              updateThreadTitle(currentThreadId, title);
            }
          }
        }
      });

      return unsubscribe;
    }
  }, [
    currentThreadId,
    runtime,
    saveThreadMessages,
    getCurrentThread,
    updateThreadTitle,
  ]);

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      {children}
    </AssistantRuntimeProvider>
  );
}

async function* processStreamedEvents(
  reader: ReadableStreamDefaultReader<Uint8Array>,
): AsyncGenerator<AssistantMessageChunk, StreamProcessingResult, undefined> {
  const decoder = new TextDecoder();

  let accumulatedText = "";
  let currentEventName: string | null = null;
  let threadId: string | null = null;

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const eventLines = chunk.split("\n").filter((line) => line.trim() !== "");

      for (const line of eventLines) {
        if (line.startsWith("event:")) {
          currentEventName = line.substring(6).trim();
          continue;
        }

        if (line.startsWith("data:")) {
          const dataContent = line.substring(5).trim();
          const parsedData = JSON.parse(dataContent);

          if (currentEventName === "metadata") {
            if (parsedData.thread_id) {
              threadId = parsedData.thread_id;
            }
            currentEventName = null;
          } else if (currentEventName === "message") {
            const contentDelta = parsedData.content || "";
            if (contentDelta) {
              accumulatedText += contentDelta;
              yield {
                content: [{ type: "text", text: accumulatedText }],
                metadata: { custom: { threadId } },
              };
            }
          }
        }
      }
    }
  } catch (e) {
    yield formatErrorMessage(
      `Stream Events processing error: ${(e as Error).message}`,
    );
  }
  return { accumulatedText, threadId };
}

function getLatestUserMessageText(
  messages: readonly ThreadMessage[],
): string | null {
  if (messages.length === 0) {
    return null;
  }
  const lastMessage = messages[messages.length - 1];
  if (lastMessage.role !== "user") {
    return null;
  }

  if (lastMessage.content) {
    for (const part of lastMessage.content) {
      if (part.type === "text") {
        return part.text;
      }
    }
  }
  return null;
}

function formatErrorMessage(message: string): AssistantMessageChunk {
  return { content: [{ type: "text", text: message }] };
}
