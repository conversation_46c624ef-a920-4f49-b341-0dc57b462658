"use client";

import { Account, useGetUserAccounts } from "@/api-hooks";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import Loader from "@/web/components/Loader";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/web/components/ui/sidebar";
import { useAccount } from "@/web/contexts/accountContext";

export default function AccountList() {
  const router = useRouter();

  const { data: accounts, isError, isLoading } = useGetUserAccounts();
  const { selectedAccount, setSelectedAccount, allAccounts, setAllAccounts } =
    useAccount();

  useEffect(() => {
    if (accounts && accounts.length > 0) {
      // Order alphabetically by crmName for now
      setAllAccounts(
        accounts.sort((a, b) => a.crmName.localeCompare(b.crmName)),
      );
    }
  }, [accounts, setAllAccounts]);

  const goToAccount = (account: Account) => {
    setSelectedAccount(account);
    router.push(`/account/${account.crmId}`);
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Accounts</SidebarGroupLabel>
      <SidebarGroupContent>
        {isLoading ? (
          <Loader />
        ) : (
          <SidebarMenu>
            {allAccounts?.map((account) => (
              <SidebarMenuItem key={account.crmId}>
                <SidebarMenuButton
                  isActive={selectedAccount?.crmId === account.crmId}
                  onClick={() => goToAccount(account)}
                >
                  {account.crmName}
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        )}
        {!isLoading && isError ? <div>Error loading accounts</div> : null}
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
