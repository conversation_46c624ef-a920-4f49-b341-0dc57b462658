"use client";

import { Account, useGetUserAccounts } from "@/api-hooks";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import Loader from "@/web/components/Loader";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/web/components/ui/sidebar";
import { useAccount } from "@/web/contexts/accountContext";
import { useThreadManager } from "@/web/contexts/threadContext";

export default function AccountList() {
  const router = useRouter();

  const { data: accounts, isError, isLoading } = useGetUserAccounts();
  const { selectedAccount, setSelectedAccount, allAccounts, setAllAccounts } =
    useAccount();
  const { getThreadsForAccount, switchThread, createThread } =
    useThreadManager();

  useEffect(() => {
    if (accounts && accounts.length > 0) {
      // Order alphabetically by crmName for now
      setAllAccounts(
        accounts.sort((a, b) => a.crmName.localeCompare(b.crmName)),
      );
    }
  }, [accounts, setAllAccounts]);

  const goToAccount = (account: Account) => {
    setSelectedAccount(account);

    // Find existing threads for this account or create a new one
    const accountThreads = getThreadsForAccount(account.crmId);
    if (accountThreads.length > 0) {
      // Switch to the most recently updated thread
      const mostRecentThread = accountThreads.sort(
        (a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
      )[0];
      switchThread(mostRecentThread.id);
    } else {
      // Create a new thread for this account
      createThread(account);
    }

    router.push(`/account/${account.crmId}`);
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Accounts</SidebarGroupLabel>
      <SidebarGroupContent>
        {isLoading ? (
          <Loader />
        ) : (
          <SidebarMenu>
            {allAccounts?.map((account) => (
              <SidebarMenuItem key={account.crmId}>
                <SidebarMenuButton
                  isActive={selectedAccount?.crmId === account.crmId}
                  onClick={() => goToAccount(account)}
                >
                  {account.crmName}
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        )}
        {!isLoading && isError ? <div>Error loading accounts</div> : null}
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
