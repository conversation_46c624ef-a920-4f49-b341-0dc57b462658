"use client";

import { PlusIcon, MessageSquareIcon, TrashIcon } from "lucide-react";
import { useEffect } from "react";

import { Button } from "@/web/components/ui/button";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/web/components/ui/sidebar";
import { useAccount } from "@/web/contexts/accountContext";
import { useThreadManager } from "@/web/contexts/threadContext";

export default function ThreadList() {
  const { selectedAccount } = useAccount();
  const {
    getThreadsForAccount,
    currentThreadId,
    createThread,
    switchThread,
    deleteThread,
  } = useThreadManager();

  const threads = selectedAccount ? getThreadsForAccount(selectedAccount.crmId) : [];

  // Auto-create a thread when an account is selected but no threads exist
  useEffect(() => {
    if (selectedAccount && threads.length === 0 && !currentThreadId) {
      createThread(selectedAccount);
    }
  }, [selectedAccount, threads.length, currentThreadId, createThread]);

  const handleCreateThread = () => {
    if (selectedAccount) {
      createThread(selectedAccount);
    }
  };

  const handleDeleteThread = (threadId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    deleteThread(threadId);
  };

  const formatThreadTitle = (title: string) => {
    return title.length > 25 ? `${title.substring(0, 25)}...` : title;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (!selectedAccount) {
    return (
      <SidebarGroup>
        <SidebarGroupLabel>Conversations</SidebarGroupLabel>
        <SidebarGroupContent>
          <div className="text-sm text-muted-foreground p-2">
            Select an account to view conversations
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="flex items-center justify-between">
        <span>Conversations</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCreateThread}
          className="h-6 w-6 p-0"
        >
          <PlusIcon className="h-4 w-4" />
        </Button>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {threads.map((thread) => (
            <SidebarMenuItem key={thread.id}>
              <SidebarMenuButton
                isActive={currentThreadId === thread.id}
                onClick={() => switchThread(thread.id)}
                className="group flex items-center justify-between w-full"
              >
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  <MessageSquareIcon className="h-4 w-4 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <div className="truncate text-sm font-medium">
                      {formatThreadTitle(thread.title)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(thread.updatedAt)}
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => handleDeleteThread(thread.id, e)}
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <TrashIcon className="h-3 w-3" />
                </Button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
          {threads.length === 0 && (
            <div className="text-sm text-muted-foreground p-2">
              No conversations yet. Click + to start a new conversation.
            </div>
          )}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
