{"name": "web", "version": "0.1.0", "private": true, "engines": {"node": "22.14.0"}, "packageManager": "pnpm@10.10.0", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@assistant-ui/react": "^0.10.5", "@assistant-ui/react-ai-sdk": "^0.10.6", "@assistant-ui/react-markdown": "^0.10.3", "@pearl-frontend/api-client": "workspace:*", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.75.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.493.0", "next": "15.2.3", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.74.7", "@types/react": "^19", "@types/react-dom": "^19", "eslint-config-next": "15.2.3", "eslint-plugin-next": "^0.0.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0"}}