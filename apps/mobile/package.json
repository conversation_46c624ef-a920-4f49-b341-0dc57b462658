{"name": "pearl", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"// ==================== START ==================== //": "", "start": "expo start -c", "android": "expo run:android", "ios": "expo run:ios", "// ==================== UPGRADE ==================== //": "", "check:tamagui": "tamagui check", "upgrade:tamagui": "yarn up '*tamagui*'@latest '@tamagui/*'@latest", "// ==================== PRETTIER ==================== //": "", "prettier:fix": "prettier --write .", "prettier:check": "prettier --check .", "// ==================== LINT ==================== //": "", "lint": "expo lint", "lint:fix": "eslint . --fix --max-warnings 0 -c eslint.config.js", "lint:check": "eslint . --max-warnings 0 -c eslint.config.js --cache --cache-strategy content --cache-location .cache/eslint.json", "lint:generate-output": "eslint . --format json --output-file eslint-output.json", "lint:disable-inserter": "eslint-disable-inserter < eslint-output.json && rm -rf eslint-output.json", "lint:auto-disable": "yarn lint:generate-output && yarn lint:disable-inserter", "// ==================== TYPESCRIPT ==================== //": "", "tsc:check": "tsc --noEmit", "// ==================== TEST ==================== //": "", "test": "jest --maxWorkers=2", "test:changed": "pnpm test --only<PERSON><PERSON>ed", "// ==================== CI ==================== //": "", "eas-build-pre-install": "echo node-linker=hoisted >> ../../.npmrc", "check:config": "npx expo config", "ci:check": "yarn tsc:check && yarn lint:check && yarn prettier:check && yarn test:check", "ci:fix": "yarn prettier:fix && yarn lint:fix", "// ==================== BUILD ==================== //": "", "build:ios:simulator": "eas build --profile development-simulator --platform ios --local", "build:android:simulator": "eas build --profile development-simulator --platform android --local"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@dev-plugins/react-query": "^0.3.1", "@expo-google-fonts/inter": "^0.3.0", "@expo/config": "^11.0.10", "@pearl/locales": "workspace:*", "@react-navigation/drawer": "^7.3.12", "@react-navigation/elements": "^2.4.2", "@react-navigation/native": "^7.1.9", "@shopify/react-native-skia": "2.0.0-next.4", "@tamagui/config": "^1.126.12", "@tamagui/lucide-icons": "^1.126.12", "@tamagui/toast": "^1.126.12", "@tanstack/react-query": "^5.75.2", "babel-preset-expo": "~13.1.11", "burnt": "^0.13.0", "dayjs": "^1.11.13", "expo": "~53.0.9", "expo-audio": "^0.4.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.13", "expo-web-browser": "~14.1.6", "i18next": "^25.1.3", "jotai": "^2.12.4", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.1", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.1", "react-native-mmkv": "^3.2.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "tamagui": "^1.126.12"}, "devDependencies": {"@babel/core": "^7.24.6", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "@expo/metro-config": "~0.20.0", "@expo/metro-runtime": "~5.0.4", "@tamagui/babel-plugin": "^1.126.12", "@tamagui/cli": "^1.126.12", "@tamagui/metro-plugin": "^1.126.12", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-typescript-sort-keys": "^3.3.0", "jest": "~29.7.0", "jest-expo": "~53.0.5", "prettier": "^3.5.3", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.8.3"}}