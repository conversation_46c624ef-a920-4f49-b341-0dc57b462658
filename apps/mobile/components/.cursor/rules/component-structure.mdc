---
description: 
globs: *.tsx
alwaysApply: false
---
---
description: React component structure and best practices
globs: "**/*.tsx"
alwaysApply: true
---

- Use functional components with TypeScript interfaces
- Use tamagui components for UI elements
- Minimize useEffect, useMemo and useCallback usage
- Use tamagui's Stack, XStack, and YStack for layout
- Use tamagui Theme provider for theming
- Utilize tamagui's animation system for transitions
- Leverage tamagui's style props instead of inline styles
- Import Text from @components/primitives/Text
- Create reusable primitive components in @components/primitives/

- Example Component Structure

```typescript
import { IconButton } from '@components/primitives/IconButtonSax';
import { useUserType } from '@hooks/useUserType';
import { HeaderBackButtonProps } from '@react-navigation/elements';
import { goBackOrGoHome } from '@utils/goBackOrGoHome';
import { Themes } from 'tamagui';

interface BackButtonProps extends HeaderBackButtonProps {
  color?: keyof Themes['light'];
}

export const BackButton = ({ color, ...props }: BackButtonProps) => {
  const userType = useUserType();

  return (
    <IconButton
      buttonId="back_button"
      name="ArrowLeft2"
      iconButtonVariant="secondary"
      height="$1"
      width="$1"
      color={color}
      onPress={() => goBackOrGoHome(userType)}
      {...props}
    />
  );
};

```
