import { ComponentProps } from "react";
import { KeyboardStickyView } from "react-native-keyboard-controller";
import { useSafeAreaInsets } from "react-native-safe-area-context";

type KeyboardStickyBoxProps = ComponentProps<typeof KeyboardStickyView> & {
  offset?: {
    closed?: number;
    opened?: number;
  };
};

/**
 * This component is used to make components stick to the keyboard
 *
 * The role of KeyboardStickyView (from react-native-keyboard-controller) is to maintain the gap
 * between the very bottom of the screen and the children of the KeyboardStickyView when the keyboard is open
 *
 * Issue explanation:
 * ```
 * With KeyboardStickyView        With KeyboardStickyBox:
 * ┌─────────────┐                ┌─────────────┐
 * │             │                │             │
 * │             │                │             │
 * │  Content    │                │  Content    │
 * │             │                │             │
 * ├─────────────┤                │             │
 * │  Sticky     │                │             │
 * │  Component  │                ├─────────────┤
 * ├─────────────┤     =>         │  Sticky     │
 * │NavBarHeight │                │  Component  │
 * ├─────────────┤                ├─────────────┤
 * │ [Keyboard]  │                │ [Keyboard]  │
 * │             │                │             │
 * └─────────────┘                └─────────────┘
 *
 * Extra gap NavBarHeight
 * (inset bottom)                 Correct gap
 * ```
 *
 * The issue is that we end up with a very big gap when there is a system navigation bar as well as a tabBar displayed
 * The role of KeyboardStickyBox is to take into account the presence of the tabBar and the system navigation bar
 * and to adjust the gap accordingly. This presumes a proper use of bottoms insets in the parent which is usually done by Layout or ScreenLayout
 *
 * If you want to apply some paddingBottom on the children only when the keyboard is closed, use the prop offset.closed
 */
export const KeyboardStickyBox = ({
  offset,
  ...props
}: KeyboardStickyBoxProps) => {
  const insets = useSafeAreaInsets();

  const openOffset = insets.bottom === 0 ? 0 : 16;

  return (
    <KeyboardStickyView
      offset={{
        opened: openOffset,
        ...offset,
      }}
      {...props}
    />
  );
};
