import { HeaderShownContext } from "@react-navigation/elements";
import { ImageProps } from "expo-image";
import { useContext } from "react";
import { ViewStyle } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Themes, View, ViewProps, getTokens } from "tamagui";

import { Background } from "@components/layout/Background";
import { BACKGROUNDS } from "@constants/assets";
import { useGetDefaultHeaderHeight } from "@hooks/useGetDefaultHeaderHeight";
import { useTheme } from "@hooks/useTheme";
import { Spacing } from "@theme/tokens";

export interface LayoutProps extends Omit<ViewProps, "backgroundColor"> {
  backgroundColor?: keyof Themes["light"];
  backgroundImageSource?: ImageProps["source"];
  isModal?: boolean;
  withBackground?: boolean;
  withDefaultPaddingTop?: boolean;
  withEdgeBottom?: boolean;
  withEdgeTop?: boolean;
  withHeader?: boolean;
  withHorizontalPadding?: boolean;
}

export const Layout = ({
  backgroundColor = "background",
  withBackground = true,
  backgroundImageSource,
  withHorizontalPadding = true,
  withEdgeTop,
  withEdgeBottom = true,
  withDefaultPaddingTop = true,
  withHeader = false,
  children,
  isModal,
  style,
  ...props
}: LayoutProps) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const { defaultHeaderHeight } = useGetDefaultHeaderHeight(isModal);
  const isParentHeaderShown = useContext(HeaderShownContext);

  // Fix for the SafeAreaView issue
  // https://github.com/th3rdwave/react-native-safe-area-context/issues/226
  const safeAreaPaddings: ViewStyle = {
    paddingTop: isParentHeaderShown
      ? defaultHeaderHeight
      : withEdgeTop === true || withEdgeTop === undefined
        ? insets.top
        : 0,
    paddingBottom: withEdgeBottom ? insets.bottom : 0,
    // TODO: replace with getToken('$layout')
    paddingHorizontal: withHorizontalPadding ? 20 : 0,
  };

  const stylePaddingTop =
    (getTokens().space[
      (props.paddingTop as Spacing) || (withDefaultPaddingTop ? "$md" : "$none")
    ]?.val as number) || 0;

  const backgroundImage = backgroundImageSource
    ? backgroundImageSource
    : theme === "dark"
      ? BACKGROUNDS.darkBackground
      : BACKGROUNDS.lightBackground;

  return (
    <View
      flex={1}
      backgroundColor={`$${backgroundColor}`}
      style={[
        safeAreaPaddings,
        {
          paddingTop:
            (safeAreaPaddings.paddingTop as number) + stylePaddingTop || 0,
        },
        ...(Array.isArray(style) ? style : [style]),
      ]}
      {...props}
    >
      {withBackground ? (
        <Background backgroundImageSource={backgroundImage} />
      ) : null}
      {children}
    </View>
  );
};
