import { DrawerContentComponentProps } from "@react-navigation/drawer";
import { LinearGradient } from "expo-linear-gradient";
import { useState } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Avatar, Image, ScrollView, Text, View, XStack } from "tamagui";

import { Layout } from "@components/layout/Layout";
import { CustomDrawerItemList } from "@components/navigation/drawer/CustomDrawerItemList";
import { ICONS } from "@constants/assets";
import { useTheme } from "@hooks/useTheme";

type CustomDrawerContentProps = DrawerContentComponentProps;

const ICON_SIZE = 24;

export const CustomDrawerContent = (props: CustomDrawerContentProps) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  const [footerHeight, setFooterHeight] = useState(0);

  // TODO: add colors to theme when we have a design system
  const fadeGradientColors =
    theme === "dark"
      ? (["#1A1B1F00", "#1A1B1F"] as const)
      : (["#F0EFF600", "#F0EFF6"] as const);

  return (
    <Layout
      withBackground={false}
      backgroundColor="drawerBackground"
      withHorizontalPadding={false}
    >
      <XStack
        gap="$md"
        alignItems="center"
        paddingVertical="$lg"
        paddingHorizontal={28}
        marginBottom={24}
      >
        <Image
          source={ICONS.appIcon}
          style={{ width: ICON_SIZE, height: ICON_SIZE }}
        />
        <Text fontWeight="500" fontSize={14} lineHeight={20}>
          {"Pearl"}
        </Text>
      </XStack>
      <ScrollView
        contentContainerStyle={{
          gap: 12,
          paddingTop: 20,
          paddingLeft: 20,
          paddingRight: 30,
        }}
        showsVerticalScrollIndicator={false}
      >
        <CustomDrawerItemList {...props} />
      </ScrollView>
      <LinearGradient
        style={{
          position: "absolute",
          bottom: insets.bottom + footerHeight,
          width: "100%",
          height: 56,
        }}
        colors={fadeGradientColors}
        locations={[0, 1]}
        pointerEvents="none"
      />
      <XStack
        alignItems="center"
        gap="$lg"
        paddingTop="$3xl"
        borderTopWidth={1}
        // TODO: add color to theme when we have a design system
        //@ts-ignore
        borderColor={theme === "dark" ? "#E0E0E00D" : "white"}
        paddingHorizontal={24}
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          setFooterHeight(height);
        }}
      >
        <Avatar>
          <Avatar.Image
            rounded="$full"
            source={{
              uri: "https://github.com/matt-west.png",
            }}
          />
        </Avatar>
        <View>
          <Text fontWeight="500" fontSize={14} lineHeight={20}>
            {"John Doe"}
          </Text>
          <Text fontWeight="400" fontSize={12} lineHeight={20} color="gray">
            {"<EMAIL>"}
          </Text>
        </View>
      </XStack>
    </Layout>
  );
};
