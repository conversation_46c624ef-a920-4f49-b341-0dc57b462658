import { useTranslation } from "react-i18next";
import { Button, YStack, Text } from "tamagui";

import { Layout } from "@components/layout/Layout";
import { useTheme } from "@hooks/useTheme";

export default function IndexScreen() {
  const { toggleTheme } = useTheme();
  const { t, i18n } = useTranslation(["screens"]);

  const handleToggleLanguage = () => {
    i18n.changeLanguage(i18n.language === "fr" ? "en" : "fr");
  };

  return (
    <Layout>
      <YStack gap={16}>
        <Button onPress={toggleTheme}>
          <Button.Text>{"Toggle Theme"}</Button.Text>
        </Button>
        <Text fontSize={32} textAlign="center">
          {t("screens:index.hello")}
        </Text>
        <Button onPress={handleToggleLanguage}>
          <Button.Text>{"Toggle Language"}</Button.Text>
        </Button>
      </YStack>
    </Layout>
  );
}
