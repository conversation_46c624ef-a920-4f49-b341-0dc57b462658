import "@utils/i18n";
import { ApiClientProvider } from "@/api-client";
import { queryClient, QueryClientProvider } from "@/api-hooks";
import { useReactQueryDevTools } from "@dev-plugins/react-query";
import { Inter_500Medium, Inter_700Bold } from "@expo-google-fonts/inter";
import { ThemeProvider } from "@react-navigation/native";
import { Activity, Home } from "@tamagui/lucide-icons";
import { ToastProvider, ToastViewport } from "@tamagui/toast";
import { useFonts } from "expo-font";
import { SplashScreen } from "expo-router";
import { Drawer } from "expo-router/drawer";
import { useEffect } from "react";
import { StatusBar } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { KeyboardProvider } from "react-native-keyboard-controller";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { TamaguiProvider, Text } from "tamagui";

import { CustomDrawerContent } from "@components/navigation/drawer/CustomDrawerContent";
import { CustomDrawerMenuButton } from "@components/navigation/drawer/CustomDrawerMenuButton";
import { CurrentToast } from "@components/toast/CurrentToast";
import { useTheme } from "@hooks/useTheme";
import { apiClient } from "@services/api/apiClient";
import { tokenStorage } from "@services/api/tokenStorage";
import {
  darkNavigationTheme,
  lightNavigationTheme,
} from "@theme/navigationTheme";

import config from "../tamagui.config";

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "index",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  useReactQueryDevTools(queryClient);

  const [interLoaded, interError] = useFonts({
    Inter: Inter_500Medium,
    InterBold: Inter_700Bold,
  });

  useEffect(() => {
    if (interLoaded || interError) {
      // Hide the splash screen after the fonts have loaded (or an error was returned) and the UI is ready.
      SplashScreen.hideAsync();
    }
  }, [interLoaded, interError]);

  if (!interLoaded && !interError) {
    return null;
  }

  return (
    <ApiClientProvider apiClient={apiClient} tokenStorage={tokenStorage}>
      <QueryClientProvider>
        <ThemeProvider
          value={theme === "dark" ? darkNavigationTheme : lightNavigationTheme}
        >
          <TamaguiProvider config={config} defaultTheme={theme}>
            <KeyboardProvider>
              <ToastProvider
                swipeDirection="horizontal"
                duration={1000}
                native="mobile"
                burntOptions={{ from: "bottom" }}
              >
                <GestureHandlerRootView style={{ flex: 1 }}>
                  <StatusBar
                    barStyle={
                      theme === "dark" ? "light-content" : "dark-content"
                    }
                  />
                  <RootLayoutNav />
                </GestureHandlerRootView>
                <CurrentToast />
                <ToastViewport
                  top={insets.top}
                  left={insets.left}
                  right={insets.right}
                  bottom={insets.bottom}
                />
              </ToastProvider>
            </KeyboardProvider>
          </TamaguiProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ApiClientProvider>
  );
}

const RootLayoutNav = () => {
  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerLeft: (props) => <CustomDrawerMenuButton {...props} />,
        headerTransparent: true,
        drawerType: "slide",
      }}
    >
      <Drawer.Screen
        name="index" // This is the name of the page and must match the url from root
        options={{
          drawerLabel: "Home",
          drawerIcon: ({ size }) => <Home size={size} />,
          title: "Home",
          headerTransparent: true,
          headerTitle: () => <Text>{"Pearl Alpha"}</Text>,
        }}
      />
      <Drawer.Screen
        name="activity" // This is the name of the page and must match the url from root
        options={{
          drawerLabel: "Activity",
          title: "Activity",
          drawerIcon: () => <Activity size={18} />,
        }}
      />
    </Drawer>
  );
};
