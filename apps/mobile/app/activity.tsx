import { ExternalLink } from "@tamagui/lucide-icons";
import {
  AudioModule,
  RecordingPresets,
  useAudioPlayer,
  useAudioRecorder,
  useAudioRecorderState,
} from "expo-audio";
import { useEffect, useState } from "react";
import { Alert } from "react-native";
import {
  Anchor,
  Button,
  H2,
  Paragraph,
  Text,
  View,
  XStack,
  YStack,
} from "tamagui";

import { ToastControl } from "@components/toast/CurrentToast";

export default function IndexScreen() {
  const [isRecording, setIsRecording] = useState(false);

  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const recordingState = useAudioRecorderState(audioRecorder);

  const player = useAudioPlayer({
    uri:
      audioRecorder?.uri || "https://download.samplelib.com/mp3/sample-3s.mp3",
  });

  const record = async () => {
    await audioRecorder.prepareToRecordAsync();
    audioRecorder.record();
    setIsRecording(true);
  };

  const stopRecording = async () => {
    // The recording will be available on `audioRecorder.uri`.
    await audioRecorder.stop();
    setIsRecording(false);
  };

  useEffect(() => {
    (async () => {
      const status = await AudioModule.requestRecordingPermissionsAsync();
      if (!status.granted) {
        Alert.alert("Permission to access microphone was denied");
      }
    })();
  }, []);

  return (
    <YStack flex={1} items="center" gap={24} px={24} pt={12} bg="$background">
      <H2>{"Tamagui + Expo"}</H2>

      <ToastControl />

      <View flex={1} justify="center" items="center">
        <Button onPress={isRecording ? stopRecording : record}>
          <Text>{"Record Sound"}</Text>
        </Button>
        <XStack justify="center" items="center">
          <Text>
            {recordingState.isRecording ? "Recording" : "Not Recording"}
          </Text>
          <Text>{recordingState.durationMillis}</Text>
        </XStack>
        <Button
          onPress={() => {
            player.seekTo(0);
            player.play();
          }}
        >
          <Text>{"Play Sound"}</Text>
        </Button>
      </View>

      <XStack items="center" justify="center" flexWrap="wrap" gap={12}>
        <Paragraph fontSize={16}>{"Add"}</Paragraph>

        <Paragraph fontSize={16} px={8} py={4} color="$blue10" bg="$blue5">
          {"tamagui.config.ts"}
        </Paragraph>

        <Paragraph fontSize={16}>{"to root and follow the"}</Paragraph>

        <XStack
          items="center"
          gap={12}
          px={12}
          py={12}
          rounded={12}
          bg="$green5"
          hoverStyle={{ bg: "$green6" }}
          pressStyle={{ bg: "$green4" }}
        >
          <Anchor
            href="https://tamagui.dev/docs/core/configuration"
            textDecorationLine="none"
            color="$green10"
            fontSize="$5"
          >
            {"Configuration guide"}
          </Anchor>
          <ExternalLink size="$1" color="$green10" />
        </XStack>

        <Paragraph fontSize="$5" text="center">
          {"to configure your themes and tokens."}
        </Paragraph>
      </XStack>
    </YStack>
  );
}
