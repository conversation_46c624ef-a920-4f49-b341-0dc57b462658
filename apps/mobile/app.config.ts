import type { ConfigContext, ExpoConfig } from "@expo/config";

export default function AppConfig({ config }: ConfigContext): ExpoConfig {
  return {
    ...config,
    name: "<PERSON>",
    slug: "pearl",
    owner: "heypearl",
    version: "1.0.0",
    orientation: "portrait",
    scheme: "pearl",
    icon: "./assets/images/icons/<EMAIL>",
    userInterfaceStyle: "automatic",
    splash: {
      image: "./assets/images/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff",
    },
    assetBundlePatterns: ["**/*"],
    ios: {
      bundleIdentifier: "com.pearl.app",
      supportsTablet: false,
      config: {
        usesNonExemptEncryption: false,
      },
      infoPlist: {
        CFBundleAllowMixedLocalizations: true,
      },
      associatedDomains: [
        "applinks:heypearl.ai",
        "applinks:app.heypearl.ai",
        "applinks:app-staging.heypearl.ai",
      ],
    },
    android: {
      package: "com.pearl.app",
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      edgeToEdgeEnabled: true,
      intentFilters: [
        {
          action: "VIEW",
          autoVerify: true,
          data: [
            {
              scheme: "https",
              host: "*.heypearl.ai",
              pathPrefix: "/*",
            },
          ],
          category: ["BROWSABLE", "DEFAULT"],
        },
      ],
    },
    locales: {
      fr: "./locales/fr/expo.json",
      en: "./locales/en/expo.json",
    },
    plugins: [
      "expo-router",
      "expo-font",
      [
        "expo-build-properties",
        {
          ios: {
            newArchEnabled: true,
          },
          android: {
            newArchEnabled: true,
          },
        },
      ],
      "expo-web-browser",
      [
        "expo-audio",
        {
          microphonePermission:
            "Allow $(PRODUCT_NAME) to access your microphone.",
        },
      ],
      "expo-localization",
    ],
    updates: {
      url: "https://u.expo.dev/6c599539-4db4-4410-bcd9-a312dbb73e8d",
    },
    runtimeVersion: {
      policy: "appVersion",
    },
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {},
      eas: {
        projectId: "6c599539-4db4-4410-bcd9-a312dbb73e8d",
      },
    },
  };
}
