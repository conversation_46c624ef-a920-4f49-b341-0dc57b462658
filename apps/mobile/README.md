# 📱 Pearl - Mobile App

iOS/Android application developed with Expo.

### 🏃‍♂️ Running on iOS

1. Create a development build using `pnpm ios`.
2. Run `pnpm start`.
3. Launch the app on the simulator or a real device connected to the same network as your mac.

### 🤖 Running on Android

1. Open android studio and start the emulator or connect a real device to your computer (check its detected with `adb devices`)

2. Create a development build using `pnpm android`.

3. Run `pnpm start`.

## 🤖 IDE with AI

We use Cursor to write code with [.cursorrules](.cursorrules) file to guide the AI.
As soon as <PERSON><PERSON><PERSON> makes a bad suggestion, fix it by precising the right suggestion in the cursorrules file.

### Privacy Mode

Cursor has a privacy mode that allows you to use the AI without sending your code to the cloud.
To enable it in the top left corner of the screen: Cursor > Preferences > Cursor Settings > Privacy Mode

## 🐛 Debugging

- Best way to debug is using the [expo debugger and other debugging tools](https://docs.expo.dev/debugging/tools/) (press `j` to open debugger in the terminal)

## 🔗 Useful Links

- We use [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)
- [React Native Documentation](https://reactnative.dev/docs/environment-setup)
- [Expo Documentation](https://docs.expo.dev/build/introduction/)
