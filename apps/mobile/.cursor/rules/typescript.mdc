---
description: 
globs: *.tsx,*.ts
alwaysApply: false
---
---
description: TypeScript standards for the project
globs: "**/*.{ts,tsx}"
alwaysApply: true
---

- Use TypeScript for all code with strict type checking
- Prefer interfaces over types for object definitions
- Avoid enums; use union types instead (e.g., type Status = 'loading' | 'success' | 'error')
- Use functional components with explicit TypeScript interfaces for props
- Prefer readonly arrays and properties when data shouldn't be mutated
