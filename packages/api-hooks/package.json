{"name": "@pearl-frontend/api-hooks", "version": "0.1.0", "description": "", "engines": {"node": "22.14.0"}, "packageManager": "pnpm@10.10.0", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@tanstack/react-query": "^5.75.2", "camelcase-keys": "^9.1.3", "react": "^19.0.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.74.7", "@types/react": "^19", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0"}}